# 🗄️ Database Setup Guide for STBackend

## 🎯 Current Status
- ✅ App deployed successfully to Fly.io
- ✅ Health endpoint working
- ❌ Database connection needed (currently using dummy credentials)

## 🆓 Free Database Options

### Option 1: PlanetScale (Recommended)
**Free Tier**: 5GB storage, 1 billion row reads/month

1. **Sign up**: https://planetscale.com/
2. **Create database**:
   ```bash
   # After signup, create a new database called "stbackend"
   ```
3. **Get connection details** from dashboard
4. **Set Fly.io secrets**:
   ```bash
   flyctl secrets set \
     MYSQL_HOST="aws.connect.psdb.cloud" \
     MYSQL_PORT="3306" \
     MYSQL_USER="your-username" \
     MYSQL_PASSWORD="your-password" \
     DATABASE_NAME="stbackend" \
     JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long" \
     FRONTEND_URL="https://your-frontend-domain.com"
   ```

### Option 2: Railway
**Free Tier**: 512MB RAM, 1GB storage

1. **Sign up**: https://railway.app/
2. **Create MySQL database**
3. **Get connection details**
4. **Set secrets** (same format as above)

### Option 3: Aiven
**Free Tier**: 1 month free trial

1. **Sign up**: https://aiven.io/
2. **Create MySQL service**
3. **Get connection details**
4. **Set secrets** (same format as above)

### Option 4: FreeSQLDatabase
**Free Tier**: 5MB storage (good for testing)

1. **Sign up**: https://www.freesqldatabase.com/
2. **Create MySQL database**
3. **Get connection details**
4. **Set secrets** (same format as above)

## 🔧 After Setting Up Database

### 1. Update Fly.io Secrets
```bash
# Replace with your actual database credentials
flyctl secrets set \
  MYSQL_HOST="your-actual-host" \
  MYSQL_PORT="3306" \
  MYSQL_USER="your-username" \
  MYSQL_PASSWORD="your-password" \
  DATABASE_NAME="your-database" \
  JWT_SECRET="generate-a-strong-secret-key-32-chars-min" \
  FRONTEND_URL="https://your-frontend-domain.com"
```

### 2. Restart the Application
```bash
flyctl machine restart 6e82e25ce65308
```

### 3. Check Logs
```bash
flyctl logs
```

### 4. Test the Application
```bash
# Health check
curl https://stbackend.fly.dev/health

# Should return: {"status":"ok","timestamp":"..."}
```

## 🔐 JWT Secret Generation
Generate a strong JWT secret:
```bash
# Option 1: Using openssl
openssl rand -base64 32

# Option 2: Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Option 3: Online generator
# Visit: https://generate-secret.vercel.app/32
```

## 📊 Database Schema
The app uses TypeORM with these entities:
- **User** - User accounts
- **Chat** - Chat rooms
- **ChatMember** - Chat membership
- **Message** - Chat messages
- **UserRelationship** - Friend relationships
- **MessageStatus** - Message read status

## 🚀 Next Steps After Database Setup

1. **Test all endpoints** using the API documentation
2. **Set up your frontend** to connect to `https://stbackend.fly.dev`
3. **Configure CORS** by updating `FRONTEND_URL` secret
4. **Monitor logs** for any issues
5. **Scale if needed** using `flyctl scale`

## 🔍 Troubleshooting

### App won't start after setting database
```bash
# Check logs
flyctl logs

# Common issues:
# - Wrong database credentials
# - Database not accessible from Fly.io
# - SSL/TLS connection issues
```

### Database connection timeout
```bash
# Check if database allows external connections
# Ensure SSL is properly configured
# Verify firewall settings
```
