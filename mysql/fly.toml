# fly.toml app configuration file generated for stbackend-mysql on 2025-06-16T10:04:31+02:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'stbackend-mysql'
primary_region = 'waw'

[build]
  dockerfile = 'Dockerfile'

[env]
  MYSQL_DATABASE = 'simple_telegram'
  MYSQL_PASSWORD = 'stbackend123'
  MYSQL_ROOT_PASSWORD = 'rootpassword123'
  MYSQL_USER = 'stbackend'

[[mounts]]
  source = 'mysql_data'
  destination = '/var/lib/mysql'

[[services]]
  protocol = ''
  internal_port = 0

  [[services.ports]]
    port = 3306

  [[services.tcp_checks]]
    interval = '15s'
    timeout = '10s'

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
