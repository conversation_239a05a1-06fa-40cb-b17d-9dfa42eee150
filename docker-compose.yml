services:
  api:
    build: ./api
    restart: always
    ports:
      - "5555:5555"
    depends_on:
      - mysqlDatabase
    volumes:
      - ./api:/app
      - /app/node_modules
    environment:
      MYSQL_HOST: mysqlDatabase
      MYSQL_PORT: "3306"
      MYSQL_USER: root
      MYSQL_PASSWORD: my_password
      DATABASE_NAME: simple_telegram
      JWT_SECRET: my_jwt_secret
      PORT: 5555
  mysqlDatabase:
    image: mysql:9.3.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: my_password
      MYSQL_DATABASE: simple_telegram
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      retries: 5
  adminer:
    image: adminer
    restart: always
    ports:
      - "8888:8080"
volumes:
  mysql_data:
