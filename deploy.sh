#!/bin/bash

# Fly.io Deployment Script for STBackend
echo "🚀 Starting deployment to Fly.io..."

# Set PATH for flyctl
export PATH="/home/<USER>/.fly/bin:$PATH"

# Check if flyctl is available
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl not found. Please install Fly.io CLI first."
    exit 1
fi

# Check if user is logged in
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Not logged in to Fly.io. Please run 'flyctl auth login' first."
    exit 1
fi

echo "✅ Fly.io CLI is ready"

# Set secrets (you'll need to provide these values)
echo "📝 Setting up secrets..."

# Prompt for database credentials
read -p "Enter MySQL Host (e.g., gateway01.eu-central-1.prod.aws.tidbcloud.com): " MYSQL_HOST
read -p "Enter MySQL Port (default 4000): " MYSQL_PORT
MYSQL_PORT=${MYSQL_PORT:-4000}
read -p "Enter MySQL Username: " MYSQL_USER
read -s -p "Enter MySQL Password: " MYSQL_PASSWORD
echo
read -p "Enter Database Name: " DATABASE_NAME
read -s -p "Enter JWT Secret (generate a strong secret): " JWT_SECRET
echo
read -p "Enter Frontend URL (e.g., https://yourfrontend.com): " FRONTEND_URL

# Set secrets in Fly.io
echo "🔐 Setting secrets in Fly.io..."
flyctl secrets set \
  MYSQL_HOST="$MYSQL_HOST" \
  MYSQL_PORT="$MYSQL_PORT" \
  MYSQL_USER="$MYSQL_USER" \
  MYSQL_PASSWORD="$MYSQL_PASSWORD" \
  DATABASE_NAME="$DATABASE_NAME" \
  JWT_SECRET="$JWT_SECRET" \
  FRONTEND_URL="$FRONTEND_URL"

if [ $? -eq 0 ]; then
    echo "✅ Secrets set successfully"
else
    echo "❌ Failed to set secrets"
    exit 1
fi

# Deploy the application
echo "🚀 Deploying application..."
flyctl deploy

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "🌐 Your app is available at: https://stbackend.fly.dev"
    echo "🔍 Health check: https://stbackend.fly.dev/health"
    echo "📊 Monitor your app: flyctl status"
    echo "📋 View logs: flyctl logs"
else
    echo "❌ Deployment failed"
    exit 1
fi
