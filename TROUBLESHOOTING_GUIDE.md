# 🔧 Troubleshooting Guide - STBackend on Fly.io

## 🚨 Common Issues and Solutions

### 1. App Won't Start / Keeps Crashing

#### Check Logs
```bash
# View recent logs
flyctl logs

# Look for specific errors
flyctl logs | grep -i error
flyctl logs | grep -i "failed"
```

#### Common Causes & Solutions
```bash
# Database connection issues
# Check if database file exists
flyctl ssh console -C "ls -la /app/data/"

# Permission issues
flyctl ssh console -C "chown -R nodejs:nodejs /app/data"

# Memory issues
flyctl scale memory 1024

# Port issues (check if app listens on correct port)
flyctl ssh console -C "netstat -tlnp"
```

### 2. API Endpoints Hanging/Timeout

#### Current Issue Status
Your app has this issue - endpoints hang instead of responding.

#### Debugging Steps
```bash
# Check if server is responding
curl -I https://stbackend.fly.dev/health

# Test with timeout
curl --max-time 5 https://stbackend.fly.dev/health

# Check server logs during request
flyctl logs --follow
# Then in another terminal:
curl https://stbackend.fly.dev/api/auth/register
```

#### Potential Fixes
```bash
# 1. Check rate limiting configuration
# The app might have trust proxy issues with Fly.io

# 2. Check middleware order
# Middleware might be blocking requests

# 3. Check validation middleware
# express-validator might be causing issues
```

### 3. Database Issues

#### Database Not Found
```bash
# Check if database file exists
flyctl ssh console -C "ls -la /app/data/"

# Check database permissions
flyctl ssh console -C "ls -la /app/data/database.sqlite"

# Create directory if missing
flyctl ssh console -C "mkdir -p /app/data && chown nodejs:nodejs /app/data"
```

#### Database Locked
```bash
# Check what's using the database
flyctl ssh console -C "lsof /app/data/database.sqlite"

# Restart the app
flyctl machine restart [MACHINE_ID]
```

#### Database Corruption
```bash
# Check database integrity
flyctl ssh console -C "sqlite3 /app/data/database.sqlite 'PRAGMA integrity_check;'"

# Backup and repair if needed
flyctl ssh console -C "cp /app/data/database.sqlite /app/data/backup.sqlite"
flyctl ssh console -C "sqlite3 /app/data/database.sqlite 'VACUUM;'"
```

### 4. Deployment Issues

#### Build Failures
```bash
# Deploy with verbose output
flyctl deploy --verbose

# Force rebuild
flyctl deploy --build-only

# Check Dockerfile syntax
docker build -t test-build .
```

#### Image Too Large
```bash
# Check image size
flyctl image show

# Optimize Dockerfile
# - Use multi-stage builds
# - Remove unnecessary files
# - Use .dockerignore
```

#### Deployment Timeout
```bash
# Increase timeout
flyctl deploy --wait-timeout 600

# Deploy without health checks temporarily
flyctl deploy --no-health-checks
```

### 5. Memory and Performance Issues

#### Out of Memory
```bash
# Check current memory usage
flyctl ssh console -C "free -h"

# Check app memory usage
flyctl ssh console -C "ps aux | grep node"

# Scale up memory
flyctl scale memory 1024
```

#### High CPU Usage
```bash
# Check CPU usage
flyctl ssh console -C "top -n 1"

# Scale CPU
flyctl scale vm shared-cpu-2x
```

#### Slow Database Queries
```bash
# Check database size
flyctl ssh console -C "du -h /app/data/database.sqlite"

# Optimize database
flyctl ssh console -C "sqlite3 /app/data/database.sqlite 'VACUUM; ANALYZE;'"
```

### 6. Network and SSL Issues

#### SSL Certificate Problems
```bash
# Check certificate
curl -vI https://stbackend.fly.dev/health

# Certificate should show *.fly.dev
# If issues, contact Fly.io support
```

#### DNS Issues
```bash
# Check DNS resolution
nslookup stbackend.fly.dev

# Check from different locations
dig stbackend.fly.dev
```

#### CORS Issues
```bash
# Test CORS headers
curl -H "Origin: https://your-frontend.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://stbackend.fly.dev/api/auth/register
```

## 🔍 Diagnostic Commands

### Health Check Script
```bash
#!/bin/bash
echo "=== STBackend Health Check ==="

echo "1. App Status:"
flyctl status

echo "2. Health Endpoint:"
curl -s https://stbackend.fly.dev/health | jq .

echo "3. Recent Logs:"
flyctl logs -n 20

echo "4. Machine Status:"
flyctl machine list

echo "5. Database File:"
flyctl ssh console -C "ls -la /app/data/"

echo "6. Disk Usage:"
flyctl ssh console -C "df -h"

echo "7. Memory Usage:"
flyctl ssh console -C "free -h"
```

### Performance Test
```bash
# Test response times
for i in {1..5}; do
  echo "Test $i:"
  curl -w "Time: %{time_total}s\n" -o /dev/null -s https://stbackend.fly.dev/health
  sleep 1
done
```

### Database Health Check
```bash
# Quick database check
flyctl ssh console -C "sqlite3 /app/data/database.sqlite '.tables'"
flyctl ssh console -C "sqlite3 /app/data/database.sqlite 'SELECT COUNT(*) FROM users;'"
flyctl ssh console -C "sqlite3 /app/data/database.sqlite 'PRAGMA integrity_check;'"
```

## 🛠️ Emergency Procedures

### App Not Responding
```bash
# 1. Check if machine is running
flyctl machine list

# 2. Restart machine
flyctl machine restart [MACHINE_ID]

# 3. If still not working, redeploy
flyctl deploy

# 4. Check logs for errors
flyctl logs
```

### Database Recovery
```bash
# 1. SSH into app
flyctl ssh console

# 2. Backup current database
cp /app/data/database.sqlite /app/data/emergency_backup.sqlite

# 3. Check integrity
sqlite3 /app/data/database.sqlite "PRAGMA integrity_check;"

# 4. If corrupted, try repair
sqlite3 /app/data/database.sqlite "VACUUM;"

# 5. If still issues, restore from backup or recreate
```

### Complete Reset
```bash
# WARNING: This will destroy all data!

# 1. Stop the app
flyctl machine stop [MACHINE_ID]

# 2. Remove database
flyctl ssh console -C "rm -f /app/data/database.sqlite"

# 3. Restart app (will recreate empty database)
flyctl machine start [MACHINE_ID]

# 4. Check logs
flyctl logs
```

## 📞 Getting Help

### Fly.io Support
```bash
# Check Fly.io status
curl -s https://status.fly.io/api/v2/status.json

# Community forum
# https://community.fly.io/

# Documentation
# https://fly.io/docs/
```

### Debug Information to Collect
When asking for help, provide:

```bash
# 1. App info
flyctl info

# 2. Recent logs
flyctl logs -n 50

# 3. Machine status
flyctl machine list

# 4. Health check result
curl -v https://stbackend.fly.dev/health

# 5. Database status
flyctl ssh console -C "ls -la /app/data/ && sqlite3 /app/data/database.sqlite '.tables'"
```

## 🎯 Quick Fixes for Current Issues

### Fix API Endpoint Hanging
The most likely cause is middleware configuration. Try:

1. **Check rate limiting configuration**
2. **Add trust proxy setting for Fly.io**
3. **Review middleware order**
4. **Test with minimal middleware**

### Immediate Test
```bash
# Test if it's a timeout issue
curl --max-time 3 https://stbackend.fly.dev/api/auth/register

# Test if it's a method issue
curl -X GET https://stbackend.fly.dev/api/auth/register
```

Your database is working perfectly - it's just the API layer that needs fixing! 🚀
