# 📋 Basic Commands Guide - STBackend on Fly.io

## 🚀 Deployment Commands

### Initial Setup (One-time)
```bash
# Install Fly.io CLI
curl -L https://fly.io/install.sh | sh
export PATH="/home/<USER>/.fly/bin:$PATH"

# Login to Fly.io
flyctl auth login

# Verify installation
flyctl version
```

### Deploy Application
```bash
# Navigate to project directory
cd /home/<USER>/Desktop/STBackend

# Deploy the app
flyctl deploy

# Deploy with verbose output
flyctl deploy --verbose

# Force rebuild and deploy
flyctl deploy --build-only
flyctl deploy --image-label latest
```

### Check Deployment Status
```bash
# Check app status
flyctl status

# Check app info
flyctl info

# List all your apps
flyctl apps list

# Check machine details
flyctl machine list
```

## 📊 Monitoring Commands

### View Logs
```bash
# View recent logs
flyctl logs

# View logs without tail (static)
flyctl logs --no-tail

# Follow logs in real-time
flyctl logs --follow

# View specific number of lines
flyctl logs -n 100
```

### Health Checks
```bash
# Test health endpoint
curl https://stbackend.fly.dev/health

# Test with formatted JSON
curl -s https://stbackend.fly.dev/health | jq .

# Check if app is responding
curl -I https://stbackend.fly.dev/health
```

### Machine Management
```bash
# List machines
flyctl machine list

# Restart machine
flyctl machine restart [MACHINE_ID]

# Stop machine
flyctl machine stop [MACHINE_ID]

# Start machine
flyctl machine start [MACHINE_ID]

# Get machine details
flyctl machine status [MACHINE_ID]
```

## 🗄️ Database Commands

### Access SQLite Database
```bash
# SSH into the machine
flyctl ssh console

# Once inside the container:
# Navigate to database location
cd /app/data

# Check if database file exists
ls -la database.sqlite

# Access SQLite database
sqlite3 database.sqlite
```

### SQLite Database Operations
```sql
-- Inside SQLite console:

-- List all tables
.tables

-- Show table schema
.schema users
.schema chats
.schema messages

-- View table structure
PRAGMA table_info(users);

-- Count records in tables
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM chats;
SELECT COUNT(*) FROM messages;

-- View recent users
SELECT id, username, email, created_at FROM users ORDER BY created_at DESC LIMIT 10;

-- View recent messages
SELECT id, content, sent_at FROM messages ORDER BY sent_at DESC LIMIT 10;

-- Exit SQLite
.exit
```

### Database Backup
```bash
# From inside the container (via SSH)
# Create backup
cp /app/data/database.sqlite /app/data/backup_$(date +%Y%m%d_%H%M%S).sqlite

# From your local machine
# Download database file
flyctl ssh sftp get /app/data/database.sqlite ./local_backup.sqlite
```

## 🔧 Configuration Commands

### Environment Variables
```bash
# List current secrets
flyctl secrets list

# Set new secrets
flyctl secrets set JWT_SECRET="your-new-secret"

# Set multiple secrets
flyctl secrets set \
  JWT_SECRET="your-jwt-secret" \
  FRONTEND_URL="https://your-frontend.com"

# Remove secret
flyctl secrets unset SECRET_NAME
```

### Scaling
```bash
# Check current scale
flyctl scale show

# Scale to specific count
flyctl scale count 1

# Scale memory
flyctl scale memory 512

# Scale CPU
flyctl scale vm shared-cpu-1x
```

## 🧪 Testing Commands

### API Testing
```bash
# Test health endpoint
curl https://stbackend.fly.dev/health

# Test registration (if working)
curl -X POST https://stbackend.fly.dev/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# Test login (if working)
curl -X POST https://stbackend.fly.dev/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test with timeout
curl --max-time 10 https://stbackend.fly.dev/api/auth/register
```

### Performance Testing
```bash
# Check response time
curl -w "@curl-format.txt" -o /dev/null -s https://stbackend.fly.dev/health

# Create curl-format.txt file:
echo "     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n" > curl-format.txt
```

## 🔍 Debugging Commands

### SSH Access
```bash
# SSH into the machine
flyctl ssh console

# Run commands inside container
flyctl ssh console -C "ls -la /app"
flyctl ssh console -C "ps aux"
flyctl ssh console -C "df -h"
```

### File System Check
```bash
# Check disk usage
flyctl ssh console -C "df -h"

# Check app directory
flyctl ssh console -C "ls -la /app"

# Check data directory
flyctl ssh console -C "ls -la /app/data"

# Check logs directory
flyctl ssh console -C "ls -la /var/log"
```

### Process Monitoring
```bash
# Check running processes
flyctl ssh console -C "ps aux"

# Check Node.js process
flyctl ssh console -C "ps aux | grep node"

# Check memory usage
flyctl ssh console -C "free -h"

# Check CPU usage
flyctl ssh console -C "top -n 1"
```

## 🛠️ Maintenance Commands

### App Management
```bash
# Open app in browser
flyctl open

# Open specific path
flyctl open /health

# Destroy app (careful!)
flyctl apps destroy stbackend
```

### Volume Management
```bash
# List volumes
flyctl volumes list

# Create volume
flyctl volumes create data --size 10

# Delete volume
flyctl volumes delete [VOLUME_ID]
```

### Networking
```bash
# Check IP addresses
flyctl ips list

# Allocate new IP
flyctl ips allocate-v4
flyctl ips allocate-v6

# Release IP
flyctl ips release [IP_ADDRESS]
```

## 📝 Quick Reference

### Most Used Commands
```bash
# Deploy
flyctl deploy

# Check status
flyctl status

# View logs
flyctl logs

# Test health
curl https://stbackend.fly.dev/health

# SSH access
flyctl ssh console

# Access database
flyctl ssh console -C "sqlite3 /app/data/database.sqlite"
```

### Emergency Commands
```bash
# Restart app
flyctl machine restart [MACHINE_ID]

# View recent errors
flyctl logs | grep -i error

# Check if app is down
curl -I https://stbackend.fly.dev/health

# Force redeploy
flyctl deploy --force-rebuild
```
