# 🔧 CORS Error Fix - Complete Guide

## 🚨 Original Error
```
Access to fetch at 'https://6847f4d3ec44b9f3493efab2.mockapi.io/api/auth/login' 
from origin 'http://************:3000' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: 
The value of the 'Access-Control-Allow-Origin' header in the response must not be the wildcard '*' 
when the request's credentials mode is 'include'.
```

## ✅ **FIXED: Backend CORS Configuration**

### Updated CORS Settings (Already Deployed)
```typescript
app.use(
  cors({
    origin: [
      process.env.FRONTEND_URL || "http://localhost:3000",
      "http://localhost:3000",
      "http://************:3000", // Your current frontend IP
      "http://127.0.0.1:3000",
      "http://0.0.0.0:3000"
    ],
    credentials: true, // Allow cookies
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  })
);
```

## 🔧 **REQUIRED: Frontend URL Fix**

### 1. Update Your Frontend API Base URL

**❌ Wrong (MockAPI):**
```javascript
const API_BASE_URL = 'https://6847f4d3ec44b9f3493efab2.mockapi.io';
```

**✅ Correct (Your Fly.io Backend):**
```javascript
const API_BASE_URL = 'https://stbackend.fly.dev';
```

### 2. Update All API Calls

**❌ Before:**
```javascript
// Login
fetch('https://6847f4d3ec44b9f3493efab2.mockapi.io/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ email, password })
});

// Register  
fetch('https://6847f4d3ec44b9f3493efab2.mockapi.io/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ username, email, password })
});
```

**✅ After:**
```javascript
// Login
fetch('https://stbackend.fly.dev/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ email, password })
});

// Register
fetch('https://stbackend.fly.dev/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ username, email, password })
});
```

## 🧪 **Test Your Fix**

### 1. Test Health Endpoint
```bash
curl https://stbackend.fly.dev/health
# Should return: {"status":"ok","timestamp":"...","database":"connected"}
```

### 2. Test CORS Headers
```bash
curl -H "Origin: http://************:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://stbackend.fly.dev/api/auth/register
```

### 3. Test Registration from Frontend
```javascript
// This should now work without CORS errors
fetch('https://stbackend.fly.dev/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({
    username: 'testuser',
    email: '<EMAIL>', 
    password: 'password123'
  })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## 📋 **Complete API Endpoints**

Update all these endpoints in your frontend:

### Authentication
```javascript
const API_BASE = 'https://stbackend.fly.dev';

// Auth endpoints
POST ${API_BASE}/api/auth/register
POST ${API_BASE}/api/auth/login
POST ${API_BASE}/api/auth/logout

// User endpoints  
GET ${API_BASE}/api/user/profile
PUT ${API_BASE}/api/user/profile
GET ${API_BASE}/api/user/search
GET ${API_BASE}/api/user/:id

// Chat endpoints
GET ${API_BASE}/api/chats
POST ${API_BASE}/api/chats
GET ${API_BASE}/api/chats/:id
PUT ${API_BASE}/api/chats/:id
DELETE ${API_BASE}/api/chats/:id

// Message endpoints
GET ${API_BASE}/api/messages/chat/:chatId
POST ${API_BASE}/api/messages
PUT ${API_BASE}/api/messages/:id
DELETE ${API_BASE}/api/messages/:id

// Relationship endpoints
GET ${API_BASE}/api/relationships
POST ${API_BASE}/api/relationships/request
PUT ${API_BASE}/api/relationships/:id/accept
DELETE ${API_BASE}/api/relationships/:id
```

## 🔍 **Troubleshooting**

### If CORS Errors Persist:

1. **Clear Browser Cache**
   - Hard refresh (Ctrl+Shift+R)
   - Clear browser cache and cookies

2. **Check Network Tab**
   - Open browser DevTools
   - Check Network tab for actual requests
   - Verify URLs are pointing to stbackend.fly.dev

3. **Verify Frontend Origin**
   - Check what origin your frontend is running on
   - Add it to the CORS origins list if different

### If API Endpoints Still Hang:

The CORS is fixed, but there's still a middleware issue causing endpoints to hang. This is a separate issue from CORS.

## 🎯 **Summary**

✅ **Backend CORS**: Fixed and deployed
❌ **Frontend URLs**: Need to be updated from MockAPI to Fly.io
⚠️ **API Hanging**: Separate middleware issue (not CORS related)

**Next Steps:**
1. Update your frontend to use `https://stbackend.fly.dev`
2. Test the endpoints
3. If still hanging, we'll fix the middleware issue

Your CORS problem is solved! 🎉
