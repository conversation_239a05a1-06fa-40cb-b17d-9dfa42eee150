# 🔍 Database Inspection Guide - SQLite on Fly.io

## 🗄️ Accessing Your SQLite Database

### Step 1: SSH into Your App
```bash
# Connect to your running app
flyctl ssh console

# You should see something like:
# Connecting to fdaa:1b:ea81:a7b:8c:36a1:4775:2... complete
# root@6e82e25ce65308:/app#
```

### Step 2: Navigate to Database
```bash
# Check current directory
pwd
# Should show: /app

# Navigate to data directory
cd /app/data

# List files
ls -la
# Should show: database.sqlite
```

### Step 3: Open SQLite Database
```bash
# Open SQLite database
sqlite3 database.sqlite

# You should see:
# SQLite version 3.x.x
# sqlite>
```

## 📊 Database Inspection Commands

### View Database Structure
```sql
-- List all tables
.tables
-- Expected output: chat_members  chats  message_status  messages  user_relationships  users

-- Show database schema
.schema

-- Show specific table schema
.schema users
.schema chats
.schema messages
.schema chat_members
.schema user_relationships
.schema message_status
```

### Table Information
```sql
-- Get table info (columns, types, etc.)
PRAGMA table_info(users);
PRAGMA table_info(chats);
PRAGMA table_info(messages);

-- Check table constraints
PRAGMA foreign_key_list(messages);
PRAGMA index_list(users);
```

### Data Inspection
```sql
-- Count records in each table
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'chats', COUNT(*) FROM chats
UNION ALL
SELECT 'messages', COUNT(*) FROM messages
UNION ALL
SELECT 'chat_members', COUNT(*) FROM chat_members
UNION ALL
SELECT 'user_relationships', COUNT(*) FROM user_relationships
UNION ALL
SELECT 'message_status', COUNT(*) FROM message_status;

-- View recent users
SELECT id, username, email, role, created_at 
FROM users 
ORDER BY created_at DESC 
LIMIT 10;

-- View all users (if few records)
SELECT * FROM users;

-- View recent chats
SELECT id, chat_type, name, created_at 
FROM chats 
ORDER BY created_at DESC 
LIMIT 10;

-- View recent messages
SELECT id, chat_id, sender_id, content, sent_at 
FROM messages 
ORDER BY sent_at DESC 
LIMIT 10;
```

### Advanced Queries
```sql
-- Users with their message count
SELECT u.username, u.email, COUNT(m.id) as message_count
FROM users u
LEFT JOIN messages m ON u.id = m.sender_id
GROUP BY u.id, u.username, u.email
ORDER BY message_count DESC;

-- Chats with member count
SELECT c.id, c.name, c.chat_type, COUNT(cm.user_id) as member_count
FROM chats c
LEFT JOIN chat_members cm ON c.id = cm.chat_id
GROUP BY c.id, c.name, c.chat_type
ORDER BY member_count DESC;

-- Recent activity (messages with user info)
SELECT m.content, u.username, m.sent_at, c.name as chat_name
FROM messages m
JOIN users u ON m.sender_id = u.id
JOIN chats c ON m.chat_id = c.id
ORDER BY m.sent_at DESC
LIMIT 20;
```

## 🛠️ Database Maintenance

### Backup Database
```bash
# Create backup with timestamp
cp database.sqlite backup_$(date +%Y%m%d_%H%M%S).sqlite

# List backups
ls -la backup_*.sqlite

# Verify backup
sqlite3 backup_20250616_123456.sqlite ".tables"
```

### Database Statistics
```sql
-- Database file size and page info
PRAGMA page_size;
PRAGMA page_count;
PRAGMA freelist_count;

-- Calculate database size
SELECT 
  page_count * page_size as size_bytes,
  (page_count * page_size) / 1024 as size_kb,
  (page_count * page_size) / 1024 / 1024 as size_mb
FROM 
  (SELECT 
    (SELECT COUNT(*) FROM pragma_page_count()) as page_count,
    (SELECT * FROM pragma_page_size()) as page_size
  );

-- Table sizes
SELECT 
  name,
  COUNT(*) as row_count
FROM sqlite_master 
WHERE type='table' 
AND name NOT LIKE 'sqlite_%'
GROUP BY name;
```

### Data Cleanup
```sql
-- Remove old test data (be careful!)
-- DELETE FROM users WHERE email LIKE '%test%';

-- Vacuum database (reclaim space)
VACUUM;

-- Analyze database (update statistics)
ANALYZE;
```

## 🔧 Troubleshooting

### Check Database Integrity
```sql
-- Check database integrity
PRAGMA integrity_check;

-- Quick check
PRAGMA quick_check;

-- Check foreign keys
PRAGMA foreign_key_check;
```

### Performance Analysis
```sql
-- Enable query timer
.timer on

-- Show query plan
EXPLAIN QUERY PLAN SELECT * FROM users WHERE email = '<EMAIL>';

-- Check indexes
SELECT name, sql FROM sqlite_master WHERE type='index';
```

### Common Issues
```bash
# Database locked error
# Check if any processes are using the database
lsof /app/data/database.sqlite

# Permission issues
ls -la /app/data/database.sqlite
chown nodejs:nodejs /app/data/database.sqlite

# Disk space issues
df -h /app/data
```

## 📋 Quick Reference Commands

### Essential SQLite Commands
```sql
-- Basic navigation
.help                 -- Show help
.tables              -- List tables
.schema              -- Show schema
.quit or .exit       -- Exit SQLite

-- Data viewing
SELECT * FROM users LIMIT 5;
SELECT COUNT(*) FROM table_name;
.mode column         -- Better column display
.headers on          -- Show column headers

-- Export data
.output users.csv
.mode csv
SELECT * FROM users;
.output stdout       -- Back to screen
```

### File Operations
```bash
# Download database to local machine
# (Run from your local terminal, not SSH)
flyctl ssh sftp get /app/data/database.sqlite ./local_database.sqlite

# Upload database to app
# flyctl ssh sftp put ./local_database.sqlite /app/data/database.sqlite
```

## 🎯 Example Inspection Session

```bash
# 1. SSH into app
flyctl ssh console

# 2. Navigate and open database
cd /app/data && sqlite3 database.sqlite

# 3. Quick overview
.tables
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM messages;

# 4. Check recent activity
SELECT username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5;

# 5. Exit
.quit
exit
```

This gives you complete control over your SQLite database on Fly.io! 🚀
