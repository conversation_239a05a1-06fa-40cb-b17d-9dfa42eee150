# 📋 STBackend API Endpoints

**Base URL**: `https://stbackend.fly.dev`

## 🔍 Health Check
- **GET** `/health` - Application health check

## 🔐 Authentication Routes (`/api/auth`)
- **POST** `/api/auth/register` - User registration
- **POST** `/api/auth/login` - User login
- **POST** `/api/auth/logout` - User logout

## 👤 User Routes (`/api/user`)
- **GET** `/api/user/profile` - Get user profile
- **PUT** `/api/user/profile` - Update user profile
- **GET** `/api/user/search` - Search users
- **GET** `/api/user/:id` - Get user by ID

## 💬 Chat Routes (`/api/chats`)
- **GET** `/api/chats` - Get user's chats
- **POST** `/api/chats` - Create new chat
- **GET** `/api/chats/:id` - Get specific chat
- **PUT** `/api/chats/:id` - Update chat
- **DELETE** `/api/chats/:id` - Delete chat
- **POST** `/api/chats/:id/members` - Add member to chat
- **DELETE** `/api/chats/:id/members/:userId` - Remove member from chat

## 📨 Message Routes (`/api/messages`)
- **GET** `/api/messages/chat/:chatId` - Get messages for a chat
- **POST** `/api/messages` - Send new message
- **PUT** `/api/messages/:id` - Update message
- **DELETE** `/api/messages/:id` - Delete message
- **PUT** `/api/messages/:id/read` - Mark message as read

## 👥 Relationship Routes (`/api/relationships`)
- **GET** `/api/relationships` - Get user relationships
- **POST** `/api/relationships/request` - Send friend request
- **PUT** `/api/relationships/:id/accept` - Accept friend request
- **PUT** `/api/relationships/:id/reject` - Reject friend request
- **DELETE** `/api/relationships/:id` - Remove relationship

## 📊 Request/Response Examples

### Authentication
```bash
# Register
curl -X POST https://stbackend.fly.dev/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"john_doe","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST https://stbackend.fly.dev/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Health Check
```bash
curl https://stbackend.fly.dev/health
```

## 🔒 Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 📝 Notes
- All endpoints return JSON responses
- Authentication required for most endpoints (except register, login, health)
- CORS is configured for cross-origin requests
- Rate limiting may be applied
- Database connection required for full functionality
