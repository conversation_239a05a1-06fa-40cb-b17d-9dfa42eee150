# fly.toml app configuration file generated for stbackend on 2025-06-16T09:17:31+02:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'stbackend'
primary_region = 'waw'

[build]
  dockerfile = "Dockerfile"

[env]
  NODE_ENV = "production"
  PORT = "5555"

[http_service]
  internal_port = 5555
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    path = "/health"
    timeout = "5s"

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[mounts]
  source = "stbackend_data"
  destination = "/app/data"
