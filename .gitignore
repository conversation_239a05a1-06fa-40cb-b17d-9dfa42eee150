# Node.js
node_modules/
npm-debug.log
yarn-error.log
package-lock.json

# Environment variables
.env
.env.local
.env.development
.env.production

# Docker
docker-compose.override.yml
*.log

# MySQL data volume
mysql_data/

# Build output
dist/
build/

# IDE and editor files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp

# OS-specific files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# TypeScript
*.tsbuildinfo