# 🚀 Fly.io Deployment Commands for STBackend

## Prerequisites
```bash
# 1. Install Fly.io CLI
curl -L https://fly.io/install.sh | sh
export PATH="/home/<USER>/.fly/bin:$PATH"

# 2. Login to Fly.io
flyctl auth login

# 3. Verify installation
flyctl version
```

## Initial Setup (Already Done)
```bash
# Initialize Fly.io app (already done)
flyctl launch --no-deploy

# Set dummy secrets for testing (already done)
flyctl secrets set \
  MYSQL_HOST="dummy" \
  MYSQL_PORT="3306" \
  MYSQL_USER="dummy" \
  MYSQL_PASSWORD="dummy" \
  DATABASE_NAME="dummy" \
  JWT_SECRET="dummy-jwt-secret" \
  FRONTEND_URL="https://localhost:3000"
```

## Production Database Setup
```bash
# Option 1: Use PlanetScale (Free Tier)
# 1. Sign up at https://planetscale.com/
# 2. Create a database
# 3. Get connection details

# Option 2: Use Railway (Free Tier)
# 1. Sign up at https://railway.app/
# 2. Create MySQL database
# 3. Get connection details

# Option 3: Use Aiven (Free Tier)
# 1. Sign up at https://aiven.io/
# 2. Create MySQL service
# 3. Get connection details
```

## Set Production Secrets
```bash
# Replace with your actual database credentials
flyctl secrets set \
  MYSQL_HOST="your-db-host.com" \
  MYSQL_PORT="3306" \
  MYSQL_USER="your-username" \
  MYSQL_PASSWORD="your-password" \
  DATABASE_NAME="your-database" \
  JWT_SECRET="your-super-secret-jwt-key-min-32-chars" \
  FRONTEND_URL="https://your-frontend-domain.com"
```

## Deployment Commands
```bash
# Deploy the application
flyctl deploy

# Check deployment status
flyctl status

# View logs
flyctl logs

# Restart if needed
flyctl machine restart [MACHINE_ID]

# Scale (if needed)
flyctl scale count 1

# Open in browser
flyctl open
```

## Monitoring Commands
```bash
# Check app status
flyctl status

# View real-time logs
flyctl logs

# View app info
flyctl info

# Check machine details
flyctl machine list

# SSH into machine (for debugging)
flyctl ssh console
```

## Database Migration (After Setting Real DB)
```bash
# The app will automatically run migrations on startup
# TypeORM synchronize is enabled for production (you may want to disable this later)
```

## Your App URLs
- **Main App**: https://stbackend.fly.dev
- **Health Check**: https://stbackend.fly.dev/health
- **API Base**: https://stbackend.fly.dev/api
