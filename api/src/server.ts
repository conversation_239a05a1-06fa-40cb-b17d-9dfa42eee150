import dotenv from "dotenv";
import { AppDataSource } from "./config/database";
import app from "./app";
import logger from "./config/logger";

// Load environment variables
if (process.env.NODE_ENV === "production") {
  dotenv.config({ path: ".env.production" });
} else {
  dotenv.config();
}

const PORT = process.env.PORT || 3000;

// Start server first, then try to connect to database
const server = app.listen(PORT, () => {
  logger.info(`Server is running on port ${PORT}`);
});

// Try to connect to database, but don't exit if it fails
AppDataSource.initialize()
  .then(() => {
    logger.info("Database connected successfully");
  })
  .catch((error) => {
    logger.error("Database connection error:", error);
    logger.warn("Server running without database connection. Some endpoints may not work.");
  });
