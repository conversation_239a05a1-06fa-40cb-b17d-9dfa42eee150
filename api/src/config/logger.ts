import winston from "winston";

const isProduction = process.env.NODE_ENV === "production";

const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    // Always use console in production (Fly.io captures console logs)
    new winston.transports.Console({
      format: isProduction
        ? winston.format.json()
        : winston.format.simple(),
    }),
  ],
});

// Only use file logging in development
if (!isProduction) {
  try {
    logger.add(new winston.transports.File({ filename: "logs/error.log", level: "error" }));
    logger.add(new winston.transports.File({ filename: "logs/combined.log" }));
  } catch (error) {
    // Fallback to console only if file logging fails
    console.warn("File logging not available, using console only");
  }
}

export default logger;
