import { DataSource } from "typeorm";
import { User } from "../models/User";
import { Chat } from "../models/Chat";
import { ChatMember } from "../models/ChatMember";
import { Message } from "../models/Message";
import { UserRelationship } from "../models/UserRelationship";
import { MessageStatus } from "../models/MessageStatus";

// Database configuration
const isProduction = process.env.NODE_ENV === "production";

console.log("Database configuration:", {
  host: process.env.MYSQL_HOST,
  port: process.env.MYSQL_PORT,
  user: process.env.MYSQL_USER,
  database: process.env.DATABASE_NAME,
  environment: process.env.NODE_ENV
});

// Use SQLite for simplicity on Fly.io, MySQL for local development
const databaseConfig = isProduction ? {
  type: "sqlite" as const,
  database: "/app/data/database.sqlite",
  entities: [User, Chat, Chat<PERSON><PERSON><PERSON>, Message, UserRelationship, MessageStatus],
  synchronize: true, // Safe for SQLite
  logging: false,
} : {
  type: "mysql" as const,
  host: process.env.MYSQL_HOST || "localhost",
  port: parseInt(process.env.MYSQL_PORT || "3306"),
  username: process.env.MYSQL_USER || "root",
  password: process.env.MYSQL_PASSWORD || "",
  database: process.env.DATABASE_NAME || "simple_telegram",
  entities: [User, Chat, ChatMember, Message, UserRelationship, MessageStatus],
  synchronize: true,
  logging: true,
};

export const AppDataSource = new DataSource(databaseConfig);
