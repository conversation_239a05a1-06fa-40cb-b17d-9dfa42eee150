{"name": "social-network-api", "version": "1.0.0", "scripts": {"start": "node dist/server.ts", "build": "tsc", "dev": "nodemon --ext ts --legacy-watch --polling-interval 1000 src/server.ts"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "typeorm": "^0.3.17", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.9", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}